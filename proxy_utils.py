#!/usr/bin/env python3
"""
Proxy Management Utilities

This script provides utilities for managing and monitoring the proxy rotation system.
"""

import json
import os
import sys
from datetime import datetime, timedelta
from proxy_manager import Proxy<PERSON>ana<PERSON>


def display_proxy_stats():
    """Display current proxy statistics"""
    try:
        proxy_manager = ProxyManager()
        stats = proxy_manager.get_proxy_stats()
        
        print("\n" + "="*80)
        print("PROXY ROTATION SYSTEM - STATUS REPORT")
        print("="*80)
        
        if not stats:
            print("No proxy statistics available.")
            return
        
        print(f"Total Proxies: {len(stats)}")
        
        available_count = sum(1 for s in stats.values() if s['available'])
        print(f"Available Proxies: {available_count}")
        print(f"In Cooldown: {len(stats) - available_count}")
        
        print("\nDetailed Proxy Status:")
        print("-" * 80)
        print(f"{'Proxy':<25} {'Status':<12} {'Usage':<8} {'Success':<8} {'Failed':<8} {'Last Used':<20}")
        print("-" * 80)
        
        for proxy_id, data in stats.items():
            status = "Available" if data['available'] else "Cooldown"
            last_used = data['last_used']
            if last_used:
                try:
                    last_used_dt = datetime.fromisoformat(last_used)
                    last_used_str = last_used_dt.strftime("%Y-%m-%d %H:%M")
                except:
                    last_used_str = "Invalid date"
            else:
                last_used_str = "Never"
            
            print(f"{proxy_id:<25} {status:<12} {data['usage_count']:<8} {data['success_count']:<8} {data['failure_count']:<8} {last_used_str:<20}")
        
        print("-" * 80)
        
    except Exception as e:
        print(f"Error displaying proxy stats: {str(e)}")


def test_proxy_rotation():
    """Test the proxy rotation system"""
    try:
        proxy_manager = ProxyManager()
        
        print("\n" + "="*60)
        print("TESTING PROXY ROTATION SYSTEM")
        print("="*60)
        
        strategies = ["least_used", "random", "sequential"]
        
        for strategy in strategies:
            print(f"\nTesting {strategy} strategy:")
            selected_proxy = proxy_manager.select_next_proxy(strategy)
            
            if selected_proxy:
                print(f"  Selected: {selected_proxy['id']}")
                print(f"  Host: {selected_proxy['host']}:{selected_proxy['port']}")
                print(f"  Username: {selected_proxy['username']}")
            else:
                print(f"  No proxy available for {strategy} strategy")
        
        print("\nTesting proxy configuration update:")
        if proxy_manager.proxies:
            test_proxy = proxy_manager.proxies[0]
            success = proxy_manager.update_extension_proxy(test_proxy)
            print(f"  Extension update: {'Success' if success else 'Failed'}")
        
    except Exception as e:
        print(f"Error testing proxy rotation: {str(e)}")


def reset_proxy_usage():
    """Reset all proxy usage statistics"""
    try:
        proxy_manager = ProxyManager()
        
        # Clear usage data
        proxy_manager.usage_data = {}
        proxy_manager._save_usage_data()
        
        print("Proxy usage statistics have been reset.")
        
    except Exception as e:
        print(f"Error resetting proxy usage: {str(e)}")


def add_proxy_interactive():
    """Interactively add a new proxy to proxy.txt"""
    try:
        print("\n" + "="*50)
        print("ADD NEW PROXY")
        print("="*50)
        
        host = input("Enter proxy host: ").strip()
        port = input("Enter proxy port: ").strip()
        username = input("Enter proxy username: ").strip()
        password = input("Enter proxy password: ").strip()
        
        if not all([host, port, username, password]):
            print("All fields are required!")
            return
        
        # Validate port
        try:
            int(port)
        except ValueError:
            print("Port must be a number!")
            return
        
        # Add to proxy.txt
        proxy_line = f"{host}:{port}:{username}:{password}\n"
        
        with open("proxy.txt", "a", encoding="utf-8") as f:
            f.write(proxy_line)
        
        print(f"Proxy {host}:{port} added successfully!")
        
    except Exception as e:
        print(f"Error adding proxy: {str(e)}")


def main():
    """Main function for proxy utilities"""
    if len(sys.argv) < 2:
        print("Proxy Management Utilities")
        print("\nUsage:")
        print("  python proxy_utils.py stats     - Display proxy statistics")
        print("  python proxy_utils.py test      - Test proxy rotation system")
        print("  python proxy_utils.py reset     - Reset proxy usage statistics")
        print("  python proxy_utils.py add       - Add new proxy interactively")
        return
    
    command = sys.argv[1].lower()
    
    if command == "stats":
        display_proxy_stats()
    elif command == "test":
        test_proxy_rotation()
    elif command == "reset":
        confirm = input("Are you sure you want to reset all proxy usage statistics? (yes/no): ")
        if confirm.lower() in ['yes', 'y']:
            reset_proxy_usage()
        else:
            print("Reset cancelled.")
    elif command == "add":
        add_proxy_interactive()
    else:
        print(f"Unknown command: {command}")
        print("Use 'stats', 'test', 'reset', or 'add'")


if __name__ == "__main__":
    main()
