"""
Debug script for success message detection
"""

import cv2
import numpy as np
import os
from detect_success_message import detect_success_by_green_region, detect_white_text_in_green_region, detect_success_combined

def analyze_image(image_path):
    """Analyze the given image for green success banner detection issues"""
    print(f"\n==== ANALYZING {image_path} ====")
    
    # Load image
    if not os.path.exists(image_path):
        print(f"Error: File {image_path} not found!")
        return
    
    img = cv2.imread(image_path)
    height, width = img.shape[:2]
    print(f"Image dimensions: {width}x{height}")
    
    # Convert to HSV for color analysis
    hsv_image = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    
    # Define green color range
    lower_green = np.array([40, 40, 40])
    upper_green = np.array([80, 255, 255])
    
    # Create mask for green pixels
    mask = cv2.inRange(hsv_image, lower_green, upper_green)
    
    # Clean up mask
    kernel = np.ones((5,5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    # Find contours
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Debug directory
    os.makedirs("debug_analysis", exist_ok=True)
    
    # Save debug images
    cv2.imwrite(f"debug_analysis/original_{os.path.basename(image_path)}", img)
    cv2.imwrite(f"debug_analysis/green_mask_{os.path.basename(image_path)}", mask)
    
    # Draw all contours on a debug image
    debug_img = img.copy()
    cv2.drawContours(debug_img, contours, -1, (0, 0, 255), 2)
    cv2.imwrite(f"debug_analysis/all_contours_{os.path.basename(image_path)}", debug_img)
    
    # Analyze each contour
    print("\nGreen regions found:")
    print(f"{'Index':<6}{'Area':<10}{'Size':<15}{'Aspect Ratio':<15}{'Y-Position %':<15}{'Success Banner?':<15}")
    print("-" * 70)
    
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = float(w) / h if h > 0 else 0
        rel_y = y / height * 100
        
        # Check success banner criteria
        is_success = (area > 5000 and w > 200 and aspect_ratio > 3.0 and rel_y > 5 and rel_y < 30)
        
        # Print detailed info
        print(f"{i:<6}{area:<10.1f}{f'{w}x{h}':<15}{aspect_ratio:<15.2f}{rel_y:<15.1f}{'✅' if is_success else '❌'}")
        
        # Draw more detailed info on image
        contour_img = img.copy()
        cv2.rectangle(contour_img, (x, y), (x+w, y+h), (0, 0, 255), 2)
        
        # Add text
        info_text = f"Area: {area:.1f}, AR: {aspect_ratio:.2f}, Y: {rel_y:.1f}%"
        cv2.putText(contour_img, info_text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
        
        # Save individual contour image
        cv2.imwrite(f"debug_analysis/contour_{i}_{os.path.basename(image_path)}", contour_img)
    
    # Run detection methods
    print("\nRunning detection methods:")
    green_result = detect_success_by_green_region(image_path, save_debug=True)
    text_result = detect_white_text_in_green_region(image_path, save_debug=True)
    combined_result = detect_success_combined(image_path)
    
    print(f"\nSummary for {image_path}:")
    print(f"Green banner detection: {'✅' if green_result else '❌'}")
    print(f"White text detection: {'✅' if text_result else '❌'}")
    print(f"Combined detection: {'✅' if combined_result else '❌'}")

if __name__ == "__main__":
    # Test both files
    analyze_image("temp_settings_check.png")
    analyze_image("temp_settings_check_error.png")
