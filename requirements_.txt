# Groups Management System Dependencies
# Generated by install_requirements.py

# Core automation packages
selenium>=4.0.0
seleniumbase>=4.0.0
webdriver-manager>=3.8.0
undetected-chromedriver>=3.4.0

# Stealth and fingerprint masking
selenium-stealth>=1.0.6
pytz>=2021.3

# HTTP and networking
requests>=2.25.0

# Data processing and utilities
numpy>=1.20.0
pillow>=8.0.0
pyautogui>=0.9.50
unidecode>=1.2.0
psutil>=5.8.0

# CAPTCHA solving (optional)
2captcha-python>=1.1.0
anticaptchaofficial>=1.0.0

# Web scraping (optional)
selenium-wire>=5.0.0

# Computer vision (optional)
opencv-python>=4.5.0

# Utilities (optional)
fake-useragent>=1.1.0
python-dotenv>=0.19.0

# Note: Some packages may require additional system dependencies
# See install_requirements.py for system-specific installation instructions
