"""
Data generator module for creating random user information.
"""
import random
import string
import requests
import logging
import config

# Set up logger
logger = logging.getLogger(__name__)

class DataGenerator:
    """Class to generate random user data for GMX registration."""

    def __init__(self):
        """Initialize the data generator."""
        self.address_data = None
        self.name_data = None

    def generate_password(self, length=None):
        """Generate a random password that meets GMX requirements.

        Args:
            length: Length of the password (default from config)

        Returns:
            str: Random password
        """
        if length is None:
            length = config.PASSWORD_LENGTH

        # Ensure password is at least 8 characters (GMX requirement)
        if length < 8:
            length = 12

        # Ensure password has at least one of each: uppercase, lowercase, digit, special char
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        # Use a more limited set of special characters that are more likely to be accepted
        special = "!@#$%&*-_=+?"

        # Generate one character from each category
        pwd = [
            random.choice(lowercase),
            random.choice(uppercase),
            random.choice(digits),
            random.choice(special)
        ]

        # Fill the rest with random characters from all categories
        # Use more letters and digits than special characters for better readability
        remaining_chars = []
        for _ in range(length - 4):
            # 70% chance of letter, 20% chance of digit, 10% chance of special
            r = random.random()
            if r < 0.35:
                remaining_chars.append(random.choice(lowercase))
            elif r < 0.7:
                remaining_chars.append(random.choice(uppercase))
            elif r < 0.9:
                remaining_chars.append(random.choice(digits))
            else:
                remaining_chars.append(random.choice(special))

        pwd.extend(remaining_chars)

        # Shuffle the password characters
        random.shuffle(pwd)

        password = ''.join(pwd)
        logger.info(f"Generated password: {password}")

        return password

    def get_random_user_data(self):
        """Get random user data from randomuser.me API.

        Returns:
            tuple: (name_data, address_data)
        """
        try:
            # Add headers to mimic a real browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
                'Cache-Control': 'no-cache'
            }

            # Make the request to randomuser.me API
            logger.info(f"Fetching random user data from: {config.RANDOMUSER_API_URL}")
            response = requests.get(config.RANDOMUSER_API_URL, headers=headers)
            response.raise_for_status()

            data = response.json()
            logger.debug(f"Received data from randomuser.me API: {data}")

            if 'results' in data and len(data['results']) > 0:
                user = data['results'][0]

                # Extract name information
                first_name = user['name']['first']
                last_name = user['name']['last']

                # Extract date of birth
                dob = user['dob']['date']  # Format: "1987-05-04T18:12:38.228Z"
                dob_parts = dob.split('T')[0].split('-')
                year = dob_parts[0]
                month = dob_parts[1]
                day = dob_parts[2]

                # Ensure the person is at least 18 years old
                if int(year) > 2006:  # Current year - 18
                    # If too young, set to 18-25 years old
                    year = str(random.randint(1998, 2006))

                # Create name data
                name_data = {
                    "first_name": first_name,
                    "last_name": last_name,
                    "birth_date": f"{day}.{month}.{year}"
                }

                # Extract address information
                street_name = user['location']['street']['name']
                street_number = user['location']['street']['number']
                city = user['location']['city']
                state = user['location']['state']
                postal_code = str(user['location']['postcode'])  # Ensure it's a string

                # Create address data
                address_data = {
                    "street": f"{street_name} {street_number}",
                    "city": city,
                    "postal_code": str(postal_code),
                    "state": state,
                    "country": "United Kingdom"
                }

                logger.info(f"Got random user data: {first_name} {last_name}, {city}")

                # Store the data
                self.name_data = name_data
                self.address_data = address_data

                return name_data, address_data

            raise ValueError("Could not extract user information from API response")

        except Exception as e:
            logger.error(f"Error getting random user data from randomuser.me: {e}")
            # Fallback to default data
            return self._get_fallback_user_data()

    def _get_fallback_user_data(self):
        """Generate fallback user data when API calls fail.

        Returns:
            tuple: (name_data, address_data)
        """
        # More comprehensive lists for fallback data
        first_names = [
            "Anna", "Max", "Laura", "Thomas", "Sarah", "Lukas", "Julia", "Felix", "Lena", "Jonas",
            "Marie", "Paul", "Sophie", "David", "Lisa", "Tobias", "Hannah", "Jan", "Katharina", "Michael",
            "Emma", "Alexander", "Maria", "Daniel", "Sophia", "Maximilian", "Johanna", "Tim", "Lea", "Philipp"
        ]

        last_names = [
            "Müller", "Schmidt", "Schneider", "Fischer", "Weber", "Meyer", "Wagner", "Becker", "Hoffmann", "Schulz",
            "Schäfer", "Koch", "Bauer", "Richter", "Klein", "Wolf", "Schröder", "Neumann", "Schwarz", "Zimmermann",
            "Braun", "Krüger", "Hofmann", "Hartmann", "Lange", "Schmitt", "Werner", "Schmitz", "Krause", "Meier"
        ]

        cities = [
            "Berlin", "München", "Hamburg", "Köln", "Frankfurt", "Stuttgart",
            "Düsseldorf", "Leipzig", "Dortmund", "Essen", "Bremen", "Dresden",
            "Hannover", "Nürnberg", "Duisburg", "Bochum", "Wuppertal", "Bielefeld",
            "Bonn", "Münster", "Karlsruhe", "Mannheim", "Augsburg", "Wiesbaden", "Aachen"
        ]

        states = [
            "Baden-Württemberg", "Bayern", "Berlin", "Brandenburg", "Bremen",
            "Hamburg", "Hessen", "Mecklenburg-Vorpommern", "Niedersachsen",
            "Nordrhein-Westfalen", "Rheinland-Pfalz", "Saarland",
            "Sachsen", "Sachsen-Anhalt", "Schleswig-Holstein", "Thüringen"
        ]

        streets = [
            "Hauptstraße", "Schulstraße", "Gartenstraße", "Bahnhofstraße", "Kirchstraße",
            "Bergstraße", "Waldstraße", "Dorfstraße", "Lindenstraße", "Birkenweg",
            "Rosenweg", "Mühlenweg", "Wiesenweg", "Eichenweg", "Tannenweg",
            "Ahornweg", "Buchenweg", "Erlenweg", "Kiefernweg", "Fichtenweg"
        ]

        # Generate random day, month, and year for birth date
        day = random.randint(1, 28)
        month = random.randint(1, 12)
        year = random.randint(1970, 2005)  # 18-53 years old

        # Generate random postal code (5 digits for Germany)
        postal_code = f"{random.randint(10000, 99999)}"

        # Fallback name data
        name_data = {
            "first_name": random.choice(first_names),
            "last_name": random.choice(last_names),
            "birth_date": f"{day:02d}.{month:02d}.{year}"
        }

        # Fallback address data
        street = random.choice(streets)
        street_number = random.randint(1, 150)
        city = random.choice(cities)
        state = random.choice(states)

        address_data = {
            "street": f"{street} {street_number}",
            "city": city,
            "postal_code": postal_code,
            "state": state,
            "country": "Deutschland"
        }

        logger.info(f"Using fallback data: {name_data['first_name']} {name_data['last_name']}, {address_data['city']}")

        # Store the data
        self.name_data = name_data
        self.address_data = address_data

        return name_data, address_data

    def get_german_address(self):
        """Get a random German address.

        Returns:
            dict: Address information
        """
        if self.address_data is None:
            self.get_random_user_data()
        return self.address_data

    def get_german_name(self):
        """Get a random German name.

        Returns:
            dict: Name information
        """
        if self.name_data is None:
            self.get_random_user_data()
        return self.name_data

    def generate_username(self, first_name=None, last_name=None):
        """Generate a username based on name or random if not provided.

        Args:
            first_name: First name to use (optional)
            last_name: Last name to use (optional)

        Returns:
            str: Generated username
        """
        if first_name is None or last_name is None:
            if self.name_data is None:
                self.get_german_name()
            first_name = self.name_data["first_name"]
            last_name = self.name_data["last_name"]

        # Clean first and last name - remove any numbers or special characters
        # This is for the username generation, not for the actual first/last name fields
        clean_first = ''.join(c for c in first_name if c.isalpha() or c == '-')
        clean_last = ''.join(c for c in last_name if c.isalpha() or c == '-')

        # If cleaning removed everything, use fallback
        if not clean_first:
            clean_first = "user"
        if not clean_last:
            clean_last = "name"

        # Generate a random number to append
        random_num = random.randint(1, 9999)

        # Create username variations
        username_options = [
            f"{clean_first.lower()}{clean_last.lower()}{random_num}",
            f"{clean_first.lower()}.{clean_last.lower()}{random_num}",
            f"{clean_first.lower()[0]}{clean_last.lower()}{random_num}",
            f"{clean_last.lower()}{clean_first.lower()}{random_num}"
        ]

        return random.choice(username_options)

    def generate_user_data(self):
        """Generate complete user data for registration.

        Returns:
            dict: Complete user data
        """
        # Get user data from randomuser.me API (or fallback)
        name_data, address_data = self.get_random_user_data()

        username = self.generate_username(
            name_data["first_name"],
            name_data["last_name"]
        )
        password = self.generate_password()

        return {
            "username": username,
            "password": password,
            "email": f"{username}@{config.GMX_DOMAIN}",
            **name_data,
            **address_data
        }
