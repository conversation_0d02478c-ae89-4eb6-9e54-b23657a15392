"""
Dynamic Proxy Management System for GMX Account Creation

This module handles proxy rotation, cooldown tracking, and dynamic proxy configuration
for the Chrome extension used in GMX account creation.
"""

import json
import os
import time
import random
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta


class ProxyManager:
    """Manages proxy rotation with cooldown periods and usage tracking"""
    
    def __init__(self, proxy_file: str = "proxy.txt", usage_file: str = "proxy_usage.json",
                 cooldown_minutes: int = 30):
        """
        Initialize the proxy manager

        Args:
            proxy_file: Path to file containing proxy configurations
            usage_file: Path to file tracking proxy usage
            cooldown_minutes: Cooldown period in minutes before reusing a proxy
        """
        self.proxy_file = proxy_file
        self.usage_file = usage_file
        self.cooldown_minutes = cooldown_minutes
        self.logger = logging.getLogger("ProxyManager")

        # Extension paths
        self.extension_path = os.path.join("extensions", "shared_proxy_extension")
        self.background_js_path = os.path.join(self.extension_path, "background.js")

        # Load proxies and usage data
        self.proxies = self._load_proxies()
        self.usage_data = self._load_usage_data()

        # Initialize random selection state for better distribution
        self._last_selected_indices = []
        self._selection_history = []

        self.logger.info(f"ProxyManager initialized with {len(self.proxies)} proxies")
    
    def _load_proxies(self) -> List[Dict[str, str]]:
        """Load proxy configurations from proxy.txt"""
        proxies = []
        
        if not os.path.exists(self.proxy_file):
            self.logger.warning(f"Proxy file {self.proxy_file} not found")
            return proxies
        
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    parts = line.split(':')
                    if len(parts) != 4:
                        self.logger.warning(f"Invalid proxy format on line {line_num}: {line}")
                        continue
                    
                    proxy = {
                        'host': parts[0].strip(),
                        'port': int(parts[1].strip()),
                        'username': parts[2].strip(),
                        'password': parts[3].strip(),
                        'id': f"{parts[0]}:{parts[1]}:{parts[2]}"  # Include username to make each proxy unique
                    }
                    proxies.append(proxy)
            
            self.logger.info(f"Loaded {len(proxies)} proxies from {self.proxy_file}")
            
        except Exception as e:
            self.logger.error(f"Error loading proxies: {str(e)}")
        
        return proxies
    
    def _load_usage_data(self) -> Dict[str, Dict]:
        """Load proxy usage tracking data"""
        if not os.path.exists(self.usage_file):
            return {}
        
        try:
            with open(self.usage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.logger.info(f"Loaded usage data for {len(data)} proxies")
                return data
        except Exception as e:
            self.logger.error(f"Error loading usage data: {str(e)}")
            return {}
    
    def _save_usage_data(self):
        """Save proxy usage tracking data"""
        try:
            with open(self.usage_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_data, f, indent=2, default=str)
            self.logger.debug("Usage data saved successfully")
        except Exception as e:
            self.logger.error(f"Error saving usage data: {str(e)}")
    
    def _is_proxy_available(self, proxy_id: str) -> bool:
        """Check if a proxy is available (not in cooldown)"""
        if proxy_id not in self.usage_data:
            return True
        
        last_used = self.usage_data[proxy_id].get('last_used')
        if not last_used:
            return True
        
        # Parse last used time
        try:
            if isinstance(last_used, str):
                last_used_time = datetime.fromisoformat(last_used)
            else:
                last_used_time = datetime.fromtimestamp(last_used)
            
            cooldown_end = last_used_time + timedelta(minutes=self.cooldown_minutes)
            return datetime.now() >= cooldown_end
            
        except Exception as e:
            self.logger.warning(f"Error parsing last used time for {proxy_id}: {e}")
            return True
    
    def get_available_proxies(self) -> List[Dict[str, str]]:
        """Get list of proxies that are not in cooldown"""
        available = []
        for proxy in self.proxies:
            if self._is_proxy_available(proxy['id']):
                available.append(proxy)

        self.logger.info(f"Found {len(available)} available proxies out of {len(self.proxies)}")
        return available

    def _select_random_proxy(self, available_proxies: List[Dict[str, str]]) -> Dict[str, str]:
        """
        Select a random proxy with improved distribution to avoid consecutive selections
        """
        if len(available_proxies) == 1:
            return available_proxies[0]

        # Keep track of recent selections to avoid immediate repeats
        max_history = min(5, len(available_proxies) // 2)

        # Filter out recently selected proxies if we have enough alternatives
        if len(self._selection_history) > 0 and len(available_proxies) > max_history:
            recent_ids = set(self._selection_history[-max_history:])
            filtered_proxies = [p for p in available_proxies if p['id'] not in recent_ids]

            if filtered_proxies:
                available_proxies = filtered_proxies

        # Use a more robust random selection with time-based seed
        import time
        random.seed(int(time.time() * 1000000) % 2**32)

        # Multiple shuffle operations for better randomization
        for _ in range(3):
            random.shuffle(available_proxies)

        selected = random.choice(available_proxies)

        # Update selection history
        self._selection_history.append(selected['id'])
        if len(self._selection_history) > max_history * 2:
            self._selection_history = self._selection_history[-max_history:]

        return selected
    
    def select_next_proxy(self, strategy: str = "least_used") -> Optional[Dict[str, str]]:
        """
        Select the next proxy to use based on strategy
        
        Args:
            strategy: Selection strategy - "least_used", "random", or "sequential"
        
        Returns:
            Selected proxy configuration or None if no proxies available
        """
        available_proxies = self.get_available_proxies()
        
        if not available_proxies:
            self.logger.warning("No available proxies found")
            return None
        
        if strategy == "random":
            selected = self._select_random_proxy(available_proxies)
        elif strategy == "sequential":
            # Sort by last used time (oldest first)
            available_proxies.sort(key=lambda p: self.usage_data.get(p['id'], {}).get('last_used', '1970-01-01'))
            selected = available_proxies[0]
        else:  # least_used (default)
            # Sort by usage count (least used first)
            available_proxies.sort(key=lambda p: self.usage_data.get(p['id'], {}).get('usage_count', 0))
            selected = available_proxies[0]
        
        self.logger.info(f"Selected proxy: {selected['id']} using {strategy} strategy")
        return selected
    
    def mark_proxy_used(self, proxy_id: str, success: bool = True):
        """Mark a proxy as used and update usage statistics"""
        if proxy_id not in self.usage_data:
            self.usage_data[proxy_id] = {
                'usage_count': 0,
                'success_count': 0,
                'failure_count': 0,
                'last_used': None
            }
        
        self.usage_data[proxy_id]['usage_count'] += 1
        self.usage_data[proxy_id]['last_used'] = datetime.now().isoformat()
        
        if success:
            self.usage_data[proxy_id]['success_count'] += 1
        else:
            self.usage_data[proxy_id]['failure_count'] += 1
        
        self._save_usage_data()
        self.logger.info(f"Marked proxy {proxy_id} as used (success: {success})")
    
    def update_extension_proxy(self, proxy: Dict[str, str]) -> bool:
        """
        Update the Chrome extension's proxy configuration by modifying background.js

        Args:
            proxy: Proxy configuration dictionary

        Returns:
            True if update successful, False otherwise
        """
        try:
            # Create proxy configuration for the extension
            proxy_config = {
                'scheme': 'http',
                'host': proxy['host'],
                'port': proxy['port'],
                'username': proxy['username'],
                'password': proxy['password']
            }

            # Update the background.js file with new proxy configuration
            self._update_background_js(proxy_config)

            self.logger.info(f"Updated extension proxy configuration: {proxy['id']}")
            return True

        except Exception as e:
            self.logger.error(f"Error updating extension proxy: {str(e)}")
            return False
    
    def _update_background_js(self, proxy_config: Dict[str, str]):
        """Update the background.js file with new proxy configuration by replacing PROXY_CONFIG"""
        try:
            # Read current background.js
            with open(self.background_js_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Create new PROXY_CONFIG object with proper formatting
            new_config = f"""const PROXY_CONFIG = {{
  scheme: "{proxy_config['scheme']}",
  host: "{proxy_config['host']}",
  port: {proxy_config['port']},
  username: "{proxy_config['username']}",
  password: "{proxy_config['password']}"
}};"""

            # Replace the existing PROXY_CONFIG using regex
            import re
            # Match the PROXY_CONFIG object including multi-line content
            pattern = r'const PROXY_CONFIG = \{[^}]*\};'

            if re.search(pattern, content, flags=re.DOTALL):
                updated_content = re.sub(pattern, new_config, content, flags=re.DOTALL)

                # Write updated content back to file
                with open(self.background_js_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)

                self.logger.debug(f"background.js updated with new proxy: {proxy_config['host']}:{proxy_config['port']}")
            else:
                self.logger.error("Could not find PROXY_CONFIG pattern in background.js")
                raise Exception("PROXY_CONFIG pattern not found")

        except Exception as e:
            self.logger.error(f"Error updating background.js: {str(e)}")
            raise
    
    def get_proxy_stats(self) -> Dict[str, Dict]:
        """Get usage statistics for all proxies"""
        stats = {}
        for proxy in self.proxies:
            proxy_id = proxy['id']
            usage = self.usage_data.get(proxy_id, {})
            
            stats[proxy_id] = {
                'host': proxy['host'],
                'port': proxy['port'],
                'usage_count': usage.get('usage_count', 0),
                'success_count': usage.get('success_count', 0),
                'failure_count': usage.get('failure_count', 0),
                'last_used': usage.get('last_used'),
                'available': self._is_proxy_available(proxy_id)
            }
        
        return stats
    
    def reset_usage_data(self):
        """Reset all proxy usage statistics"""
        self.usage_data = {}
        self._save_usage_data()
        self.logger.info("Proxy usage statistics have been reset")

    def rotate_proxy(self, strategy: str = "random") -> Optional[Dict[str, str]]:
        """
        Main method to rotate to the next available proxy

        Args:
            strategy: Selection strategy for proxy rotation

        Returns:
            Selected proxy configuration or None if no proxies available
        """
        # Select next proxy
        selected_proxy = self.select_next_proxy(strategy)

        if not selected_proxy:
            return None

        # Update extension configuration
        if self.update_extension_proxy(selected_proxy):
            return selected_proxy
        else:
            return None
