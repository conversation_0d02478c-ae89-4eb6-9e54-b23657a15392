"""
Auto Clicker using PyAutoGUI
A reusable script for automating mouse clicks based on image detection.
"""

import pyautogui
import time
import os
import requests
import base64
from typing import Optional, Tuple
from solvecaptcha import Solvecaptcha




class ImageClicker:
    """A class for automating mouse clicks based on image detection."""
    def __init__(self, confidence: float = 0.7, wait_time: int = 5):
        """
        Initialize the ImageClicker.
        
        Args:
            confidence (float): Confidence level for image matching (0.0 to 1.0)
            wait_time (int): Default wait time before searching for images
        """
        self.confidence = confidence
        self.wait_time = wait_time
        # Disable pyautogui fail-safe for smoother operation
        pyautogui.FAILSAFE = True
        
    def wait_and_click_image(self, image_path: str, wait_time: Optional[int] = None, 
                           click_delay: float = 0.1) -> bool:
        """
        Wait for specified time, then locate and click an image on screen.
        
        Args:
            image_path (str): Path to the image file to search for
            wait_time (int, optional): Time to wait before searching (uses default if None)
            click_delay (float): Delay between mouse move and click
            
        Returns:
            bool: True if image was found and clicked, False otherwise
        """
        if wait_time is None:
            wait_time = self.wait_time
            
        print(f"Waiting {wait_time} seconds before searching for image...")
        time.sleep(wait_time)
        
        return self.click_image(image_path, click_delay)
    
    def click_image(self, image_path: str, click_delay: float = 0.1) -> bool:
        """
        Locate and click an image on screen immediately.
        
        Args:
            image_path (str): Path to the image file to search for
            click_delay (float): Delay between mouse move and click
            
        Returns:
            bool: True if image was found and clicked, False otherwise
        """
        if not os.path.exists(image_path):
            print(f"Error: Image file '{image_path}' not found.")
            return False
            
        print(f"Searching for image: {image_path}")
        
        try:
            location = pyautogui.locateCenterOnScreen(image_path, confidence=self.confidence)
            
            if location is not None:
                print(f"Image found at location: {location}")
                pyautogui.moveTo(location)
                time.sleep(click_delay)
                pyautogui.click()
                print("Image clicked successfully!")
                return True
            else:
                print("Image not found on the screen.")
                return False
                
        except Exception as e:
            print(f"Error occurred while searching for image: {e}")
            return False
    
    def wait_for_image(self, image_path: str, timeout: int = 30, 
                      check_interval: float = 1.0) -> Optional[Tuple[int, int]]:
        """
        Wait for an image to appear on screen within a timeout period.
        
        Args:
            image_path (str): Path to the image file to search for
            timeout (int): Maximum time to wait in seconds
            check_interval (float): Time between checks in seconds
            
        Returns:
            Tuple[int, int] or None: Location coordinates if found, None if timeout
        """
        if not os.path.exists(image_path):
            print(f"Error: Image file '{image_path}' not found.")
            return None
            
        print(f"Waiting for image to appear: {image_path} (timeout: {timeout}s)")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                location = pyautogui.locateCenterOnScreen(image_path, confidence=self.confidence)
                if location is not None:
                    print(f"Image appeared at location: {location}")
                    return location
            except Exception as e:
                print(f"Error while waiting for image: {e}")
                break
                
            time.sleep(check_interval)
        
        print(f"Timeout: Image not found within {timeout} seconds.")
        return None
    
    def click_multiple_images(self, image_paths: list, delay_between_clicks: float = 1.0) -> int:
        """
        Click multiple images in sequence.
        
        Args:
            image_paths (list): List of image file paths to click
            delay_between_clicks (float): Delay between each click
            
        Returns:
            int: Number of images successfully clicked
        """
        successful_clicks = 0
        
        for i, image_path in enumerate(image_paths):
            print(f"\nProcessing image {i+1}/{len(image_paths)}: {image_path}")
            
            if self.click_image(image_path):
                successful_clicks += 1
                if i < len(image_paths) - 1:  # Don't delay after the last click
                    time.sleep(delay_between_clicks)
            else:
                print(f"Failed to click image: {image_path}")
        
        print(f"\nSummary: Successfully clicked {successful_clicks}/{len(image_paths)} images.")
        return successful_clicks
    
    def set_confidence(self, confidence: float):
        """Set the confidence level for image matching."""
        if 0.0 <= confidence <= 1.0:
            self.confidence = confidence
            print(f"Confidence level set to: {confidence}")
        else:
            print("Error: Confidence must be between 0.0 and 1.0")
    
    def get_screen_size(self) -> Tuple[int, int]:
        """Get the current screen size."""
        return pyautogui.size()
    
    def take_screenshot(self, filename: str = "screenshot.png"):
        """Take a screenshot and save it to file."""
        screenshot = pyautogui.screenshot()
        screenshot.save(filename)
        print(f"Screenshot saved as: {filename}")
    
    def take_debug_screenshot(self, prefix: str = "debug"):
        """Take a timestamped screenshot for debugging purposes."""
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{prefix}_{timestamp}.png"
        screenshot = pyautogui.screenshot()
        screenshot.save(filename)
        print(f"Debug screenshot saved as: {filename}")
        return filename
    
    def show_all_matches(self, image_path: str, confidence: float = None) -> list:
        """
        Find all instances of an image on screen and show their locations.
        
        Args:
            image_path (str): Path to the image file to search for
            confidence (float): Override confidence level (uses instance default if None)
            
        Returns:
            list: List of all matching locations found
        """
        if not os.path.exists(image_path):
            print(f"Error: Image file '{image_path}' not found.")
            return []
            
        search_confidence = confidence if confidence is not None else self.confidence
        print(f"Searching for ALL instances of: {image_path} (confidence: {search_confidence})")
        
        try:
            # Get all matches
            matches = list(pyautogui.locateAllOnScreen(image_path, confidence=search_confidence))
            
            if matches:
                print(f"Found {len(matches)} match(es):")
                for i, match in enumerate(matches):
                    center = pyautogui.center(match)
                    print(f"  Match {i+1}: {match} (center: {center})")
            else:
                print("No matches found.")
                
            return matches
            
        except Exception as e:
            print(f"Error while searching for image: {e}")
            return []
    
    def debug_image_detection(self, image_path: str, take_screenshot: bool = True) -> dict:
        """
        Comprehensive debugging for image detection issues.
        
        Args:
            image_path (str): Path to the image file to search for
            take_screenshot (bool): Whether to take a screenshot for comparison
            
        Returns:
            dict: Debug information including matches, confidence levels, etc.
        """
        debug_info = {
            'image_path': image_path,
            'image_exists': os.path.exists(image_path),
            'confidence': self.confidence,
            'matches': [],
            'screenshot_taken': False,
            'screenshot_path': None
        }
        
        print(f"🔍 DEBUGGING IMAGE DETECTION")
        print(f"=" * 40)
        print(f"Image: {image_path}")
        print(f"Confidence: {self.confidence}")
        print(f"Image exists: {debug_info['image_exists']}")
        
        if not debug_info['image_exists']:
            print("❌ Image file not found!")
            return debug_info
        
        # Take a screenshot for comparison
        if take_screenshot:
            debug_info['screenshot_path'] = self.take_debug_screenshot("debug_detection")
            debug_info['screenshot_taken'] = True
        
        # Try different confidence levels
        confidence_levels = [0.9, 0.8, 0.7, 0.6, 0.5]
        
        for conf in confidence_levels:
            print(f"\n🎯 Testing confidence level: {conf}")
            matches = self.show_all_matches(image_path, conf)
            if matches:
                debug_info['matches'] = matches
                debug_info['working_confidence'] = conf
                print(f"✅ Found matches at confidence {conf}")
                break
        else:
            print("❌ No matches found at any confidence level")
        
        # Show image file info
        try:
            from PIL import Image
            img = Image.open(image_path)
            print(f"\n📊 Image info:")
            print(f"  Size: {img.size}")
            print(f"  Mode: {img.mode}")
            print(f"  Format: {img.format}")
        except Exception as e:
            print(f"Could not read image info: {e}")
        
        print(f"\n📱 Screen info:")
        screen_size = self.get_screen_size()
        print(f"  Screen size: {screen_size}")
        
        return debug_info
    def solve_captcha_with_solvecaptcha(self, image_path: str, api_key: str, 
                                       numeric: int = 0, phrase: int = 0,
                                       min_len: int = 0, max_len: int = 0,
                                       case_sensitive: int = 0, lang: str = None,
                                       calc: int = 0) -> Optional[str]:
        """
        Solve captcha using the official SolveCaptcha package.
        
        Args:
            image_path (str): Path to the captcha image file
            api_key (str): Your SolveCaptcha API key
            numeric (int): Type of captcha (0=not specified, 1=numbers only, 2=letters only)
            phrase (int): 0=one word, 1=multiple words
            min_len (int): Minimum length of expected answer
            max_len (int): Maximum length of expected answer
            case_sensitive (int): 0=not case sensitive, 1=case sensitive
            lang (str): Language code for the captcha text
            calc (int): 0=not a calculation, 1=is a calculation
            
        Returns:
            str or None: The solved captcha text, or None if failed
        """
        if not os.path.exists(image_path):
            print(f"Error: Captcha image file '{image_path}' not found.")
            return None
            
        print(f"Solving captcha using official SolveCaptcha package: {image_path}")
        
        try:
            # Initialize the solver with the API key
            solver = Solvecaptcha(api_key)
            
            # Prepare solver parameters
            params = {
                'numeric': numeric,
                'phrase': phrase,
                'minLen': min_len,
                'maxLen': max_len,
                'caseSensitive': case_sensitive,
                'calc': calc
            }
            
            # Add language parameter if specified
            if lang:
                params['lang'] = lang
                
            # Remove any parameters that are 0 or None to use defaults
            params = {k: v for k, v in params.items() if v}
            
            # Submit the captcha using the normal method
            print("Submitting captcha to SolveCaptcha service...")
            result = solver.normal(image_path, **params)
            
            if result:
                print(f"✅ Captcha solved: {result}")
                return result
            else:
                print("❌ Failed to solve captcha")
                return None
                
        except Exception as e:
            print(f"Error solving captcha: {e}")
            return None
      # The solve_captcha_base64 method was removed since we're using the official package now
    
    def capture_full_screen_debug(self, filename: str = None) -> str:
        """
        Capture a full screenshot for debugging purposes.
        
        Args:
            filename (str): Custom filename for the screenshot
            
        Returns:
            str: Path to the saved screenshot
        """
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"debug_screenshot_{timestamp}.png"
        
        # Ensure debug directory exists
        debug_dir = "debug_screenshots"
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)
        
        filepath = os.path.join(debug_dir, filename)
        screenshot = pyautogui.screenshot()
        screenshot.save(filepath)
        print(f"Debug screenshot saved: {filepath}")
        return filepath
    
    def analyze_screen_for_image(self, image_path: str, save_analysis: bool = True) -> dict:
        """
        Analyze the current screen for the target image and provide detailed debugging info.
        
        Args:
            image_path (str): Path to the image to search for
            save_analysis (bool): Whether to save debug screenshots
            
        Returns:
            dict: Analysis results including locations, confidence, etc.
        """
        print(f"\n=== DEBUGGING IMAGE DETECTION ===")
        print(f"Target image: {image_path}")
        print(f"Image exists: {os.path.exists(image_path)}")
        print(f"Current confidence: {self.confidence}")
        
        results = {
            'image_path': image_path,
            'image_exists': os.path.exists(image_path),
            'confidence_used': self.confidence,
            'matches_found': 0,
            'best_match': None,
            'all_matches': [],
            'screen_size': pyautogui.size(),
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if not os.path.exists(image_path):
            print(f"ERROR: Image file not found: {image_path}")
            return results
        
        # Take current screenshot
        if save_analysis:
            screenshot_path = self.capture_full_screen_debug(f"analysis_{os.path.basename(image_path)}_{int(time.time())}.png")
            results['screenshot_path'] = screenshot_path
        
        # Try to find all matches with different confidence levels
        confidence_levels = [0.9, 0.8, 0.7, 0.6, 0.5]
        
        for conf in confidence_levels:
            print(f"\nTrying confidence level: {conf}")
            try:
                # Find all matches at this confidence level
                matches = list(pyautogui.locateAllOnScreen(image_path, confidence=conf))
                if matches:
                    print(f"Found {len(matches)} matches at confidence {conf}")
                    for i, match in enumerate(matches):
                        center = pyautogui.center(match)
                        print(f"  Match {i+1}: {match} -> Center: {center}")
                        results['all_matches'].append({
                            'confidence': conf,
                            'box': match,
                            'center': center
                        })
                    
                    if results['best_match'] is None:
                        results['best_match'] = {
                            'confidence': conf,
                            'box': matches[0],
                            'center': pyautogui.center(matches[0])
                        }
                    break
                else:
                    print(f"No matches found at confidence {conf}")
            except Exception as e:
                print(f"Error at confidence {conf}: {e}")
        
        results['matches_found'] = len(results['all_matches'])
        
        # Try with region detection
        print(f"\n=== SCREEN ANALYSIS ===")
        print(f"Screen size: {results['screen_size']}")
        
        return results
    
    def debug_captcha_detection(self, captcha_dialog_image: str) -> bool:
        """
        Comprehensive debugging for captcha dialog detection.
        
        Args:
            captcha_dialog_image (str): Path to captcha dialog image
            
        Returns:
            bool: True if captcha was detected, False otherwise
        """
        print(f"\n{'='*50}")
        print(f"CAPTCHA DETECTION DEBUG SESSION")
        print(f"{'='*50}")
        
        # 1. Analyze current screen
        analysis = self.analyze_screen_for_image(captcha_dialog_image, save_analysis=True)
        
        # 2. Wait a moment and try again
        print(f"\nWaiting 3 seconds and trying again...")
        time.sleep(3)
        analysis2 = self.analyze_screen_for_image(captcha_dialog_image, save_analysis=True)
        
        # 3. Try clicking in different areas that might trigger captcha
        print(f"\nTrying to find ANY images on screen for reference...")
        
        # 4. List all files in screens directory
        screens_dir = "screens"
        if os.path.exists(screens_dir):
            print(f"\nAvailable reference images in {screens_dir}:")
            for file in os.listdir(screens_dir):
                if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    filepath = os.path.join(screens_dir, file)
                    print(f"  - {file}")
                    
                    # Quick test if any of these are visible
                    try:
                        result = pyautogui.locateOnScreen(filepath, confidence=0.6)
                        if result:
                            center = pyautogui.center(result)
                            print(f"    ✓ FOUND: {file} at {center}")
                        else:
                            print(f"    ✗ Not found: {file}")
                    except Exception as e:
                        print(f"    ! Error testing {file}: {e}")
        
        # 5. Summary
        print(f"\n{'='*30} SUMMARY {'='*30}")
        print(f"Captcha dialog matches found: {analysis['matches_found']}")
        if analysis['best_match']:
            print(f"Best match location: {analysis['best_match']['center']}")
            print(f"Best match confidence: {analysis['best_match']['confidence']}")
            return True
        else:
            print(f"❌ NO CAPTCHA DIALOG DETECTED")
            print(f"💡 Check the debug screenshots in 'debug_screenshots' folder")
            print(f"💡 Verify your captcha_dialog.png image is correct")
            print(f"💡 Try taking a fresh screenshot when captcha appears")
            return False
    
    def wait_for_captcha(self, captcha_dialog_image: str, timeout: int = 30, 
                       check_interval: float = 1.0) -> Optional[Tuple[int, int]]:
        """
        Wait for captcha dialog to appear with adaptive confidence levels.
        This method tries multiple confidence levels to improve detection reliability.
        
        Args:
            captcha_dialog_image (str): Path to the captcha dialog image
            timeout (int): Maximum time to wait in seconds
            check_interval (float): Time between checks in seconds
            
        Returns:
            Tuple[int, int] or None: Location coordinates if found, None if timeout
        """
        if not os.path.exists(captcha_dialog_image):
            print(f"Error: Captcha dialog image '{captcha_dialog_image}' not found.")
            return None
            
        print(f"🔍 Waiting for captcha dialog to appear: {captcha_dialog_image}")
        print(f"   Timeout: {timeout}s, Check interval: {check_interval}s")
        
        start_time = time.time()
        confidence_levels = [0.7, 0.6, 0.8, 0.5, 0.9]  # Start with most likely to work
        
        attempt = 0
        while time.time() - start_time < timeout:
            attempt += 1
            elapsed = time.time() - start_time
            print(f"   Attempt {attempt} ({elapsed:.1f}s elapsed)...")
            
            # Try different confidence levels on each attempt
            for conf in confidence_levels:
                try:
                    location = pyautogui.locateCenterOnScreen(captcha_dialog_image, confidence=conf)
                    if location is not None:
                        print(f"✅ Captcha dialog found at {location} (confidence: {conf})")
                        return location
                except Exception as e:
                    # Silent fail and try next confidence level
                    continue
            
            # If we've been waiting a while, take a debug screenshot
            if attempt == 5:
                debug_path = self.capture_full_screen_debug(f"captcha_wait_debug_{int(time.time())}.png")
                print(f"   📸 Debug screenshot saved: {debug_path}")
            
            time.sleep(check_interval)
        
        print(f"⏰ Timeout: Captcha dialog not found within {timeout} seconds.")
        print(f"💡 Try checking the debug screenshots in 'debug_screenshots' folder")
        return None
    
    def capture_captcha_area(self, x: int, y: int, width: int, height: int, filename: str = "captcha.png") -> str:
        """
        Capture a specific area of the screen (for captcha).
        
        Args:
            x (int): X coordinate of the top-left corner
            y (int): Y coordinate of the top-left corner
            width (int): Width of the area to capture
            height (int): Height of the area to capture
            filename (str): Name of the file to save the captcha image
            
        Returns:
            str: Path to the saved captcha image
        """
        screenshot = pyautogui.screenshot(region=(x, y, width, height))
        screenshot.save(filename)
        print(f"Captcha image saved as: {filename}")
        return filename


# Convenience functions for quick use
def quick_click(image_path: str, confidence: float = 0.8, wait_time: int = 5) -> bool:
    """
    Quick function to click an image with default settings.
    
    Args:
        image_path (str): Path to the image file to search for
        confidence (float): Confidence level for image matching
        wait_time (int): Time to wait before searching
        
    Returns:
        bool: True if image was found and clicked, False otherwise
    """
    clicker = ImageClicker(confidence=confidence, wait_time=wait_time)
    return clicker.wait_and_click_image(image_path)


def click_now(image_path: str, confidence: float = 0.8) -> bool:
    """
    Quick function to click an image immediately without waiting.
    
    Args:
        image_path (str): Path to the image file to search for
        confidence (float): Confidence level for image matching
        
    Returns:
        bool: True if image was found and clicked, False otherwise
    """
    clicker = ImageClicker(confidence=confidence)
    return clicker.click_image(image_path)


def handle_captcha_and_save_settings(clicker, max_attempts=3):
    """
    Reusable function to handle captcha detection, solving, and verification.
    Will retry if the captcha solution is incorrect.
    
    Args:
        clicker (ImageClicker): Instance of the ImageClicker class
        max_attempts (int): Maximum number of attempts to solve the captcha
        
    Returns:
        bool: True if successfully completed, False otherwise
    """
    location = clicker.wait_for_captcha("screens/captcha_dialog.png", timeout=30)
    if not location:
        print("No captcha dialog detected")
        return False
        
    print("Captcha dialog detected!")
    
    time.sleep(3)
    
    # API key for captcha solving service
    API_KEY = "6d471eb76317bea61bc7fc56bc18d1e6"
    
    # Pre-defined captcha area coordinates
    captcha_x = 1096
    captcha_y = 743
    captcha_width = 300          
    captcha_height = 80
    
    for attempt in range(max_attempts):
        print(f"\n🔄 Captcha solving attempt {attempt + 1}/{max_attempts}")
        time.sleep(1)  # Brief pause before starting
        
        # Capture the current captcha
        captcha_image = clicker.capture_captcha_area(
            captcha_x, captcha_y, 
            captcha_width, captcha_height, 
            "current_captcha.png"
        )
        
        # Solve the captcha using the official package
        solution = clicker.solve_captcha_with_solvecaptcha(
            captcha_image, 
            API_KEY,
            numeric=0,
            phrase=0,
            lang=0
        )
        
        if not solution:
            print(f"❌ Failed to solve captcha on attempt {attempt + 1}")
            continue
            
        print(f"🎉 Captcha solution: {solution}")
        
        # Find and click the input field
        input_location = clicker.wait_for_image("screens/captcha_input.png", timeout=15)
        if not input_location:
            print("Captcha input field not found")
            continue
            
        print(f"📍 Input field found at: {input_location}")
        
        # Click and focus the input field
        pyautogui.click(input_location)
        time.sleep(0.5)
        
        # Test if field is focused
        test_char = 'x'
        pyautogui.typewrite(test_char)
        time.sleep(0.2)
        pyautogui.press('backspace')
        time.sleep(0.3)
        
        # Clear any existing text thoroughly
        print("🧹 Clearing existing text...")
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        pyautogui.press('delete')
        time.sleep(0.3)
        pyautogui.hotkey('ctrl', 'a')  # Double clear
        time.sleep(0.2)
        pyautogui.press('delete')
        time.sleep(0.5)
        
        # Type the captcha solution
        print(f"Attempting to type solution: '{solution}'")
        try:
            for i, char in enumerate(solution['code']):
                print(f"  Typing character {i+1}/{len(solution['code'])}: '{char}'")
                pyautogui.typewrite(char)
                time.sleep(0.2)
            print("✅ Typing completed")
            time.sleep(0.5)
        except Exception as e:
            print(f"❌ Typing failed: {e}")
            continue
            
        # Click the continue button
        clicker.wait_and_click_image("screens/captcha_dialog.png", wait_time=3)
        
        # Take a screenshot to check results
        print("Waiting 0.5 seconds then taking a screenshot to check results...")
        time.sleep(0.5)
        temp_screenshot = "temp_settings_check.png"
        pyautogui.screenshot().save(temp_screenshot)
        
        # Check if code was incorrect
        try:
            code_incorrect = pyautogui.locate("screens/code_incorrect.png", temp_screenshot, confidence=0.7)
            if code_incorrect:
                print("❌ Captcha code was incorrect. Retrying...")
                # Clean up the temporary screenshot
                if os.path.exists(temp_screenshot):
                    os.remove(temp_screenshot)
                continue  # Try again with a new captcha solution
                      # Check if settings were saved successfully - First try traditional image detection
            settings_saved = pyautogui.locate("screens/settings_saved.png", temp_screenshot, confidence=0.7)
            
            # If traditional detection fails, try the improved method
            if not settings_saved:
                print("Traditional detection didn't find success message, trying improved methods...")
                try:
                    # Import success detection module for more robust detection
                    from detect_success_message import detect_success_combined
                    settings_saved = detect_success_combined(temp_screenshot)
                except ImportError:
                    print("⚠️ Improved detection module not available, using traditional method only")
            
            if settings_saved:
                print("✅ Settings were successfully saved!")
                print("LOGOUT")
                clicker.wait_and_click_image("screens/logout.png", wait_time=5)
                # Clean up the temporary screenshot
                if os.path.exists(temp_screenshot):
                    os.remove(temp_screenshot)
                return True  # Success! No need for further attempts
            else:
                print("⚠️ Settings saved confirmation not detected in the screenshot")
                print("Checking for code_incorrect...")
        except Exception as e:
            print(f"Error checking screenshot: {e}")
        
        # Clean up the temporary screenshot
        try:
            if os.path.exists(temp_screenshot):
                os.remove(temp_screenshot)
        except Exception as e:
            print(f"Error removing temporary screenshot: {e}")
    
    # If we get here, we've exhausted all attempts
    print(f"⚠️ All {max_attempts} captcha solving attempts failed")
    print("Attempting to logout anyway...")
    clicker.wait_and_click_image("screens/logout.png", wait_time=5)
    return False


if __name__ == "__main__":
    # Example usage
    print("PyAutoGUI Auto Clicker")
    print("=" * 30)
    
    # Create an instance of ImageClicker
    clicker = ImageClicker(confidence=0.8, wait_time=3)

    clicker.wait_and_click_image("screens/settings_icon.png", wait_time=3)

    clicker.wait_and_click_image("screens/pop3.png", wait_time=5)
    
    clicker.wait_and_click_image("screens/enable_pop3.png", wait_time=2)
    
    clicker.wait_and_click_image("screens/save.png", wait_time=1)
    
    # Call the captcha handling function with maximum 3 attempts
    handle_captcha_and_save_settings(clicker, max_attempts=3)
    
    # Example 1: Click a single image
    # clicker.wait_and_click_image("button.png")
    
    # Example 2: Click multiple images in sequence
    # images_to_click = ["button1.png", "button2.png", "button3.png"]
    # clicker.click_multiple_images(images_to_click, delay_between_clicks=2.0)
    
    # Example 3: Wait for an image to appear, then click it
    # location = clicker.wait_for_image("target.png", timeout=10)
    # if location:
    #     pyautogui.click(location)
    
    # Example 4: Using convenience functions
    # quick_click("my_button.png", confidence=0.9, wait_time=2)
    # click_now("immediate_button.png")


