"""
Improved Success Message Detection using Multiple Methods

This script provides multiple methods for detecting the 'Settings were saved successfully' message:
1. OCR - Optical Character Recognition to detect the text
2. Green Check Detection - Finds the green checkmark icon
3. Color Region Detection - Detects the green banner

With improved detection parameters based on analysis of success and error screenshots.
"""

import pyautogui
import cv2
import numpy as np
import os
import time
from PIL import Image

# Uncomment to use Tesseract OCR if installed
# import pytesseract
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def detect_success_by_green_region(screenshot_path=None, save_debug=True):
    """
    Detects the green success banner by its distinctive color and shape
    
    Args:
        screenshot_path (str): Path to existing screenshot, or None to take a new one
        save_debug (bool): Whether to save debug images
        
    Returns:
        bool: True if green success banner detected, False otherwise
    """
    # Make debug directory
    os.makedirs("debug_screenshots", exist_ok=True)
    
    # Take screenshot or load existing
    if screenshot_path and os.path.exists(screenshot_path):
        screenshot = cv2.imread(screenshot_path)
        print(f"Using existing screenshot: {screenshot_path}")
    else:
        screenshot_path = "debug_screenshots/current_screen.png"
        screenshot = pyautogui.screenshot()
        screenshot.save(screenshot_path)
        screenshot = cv2.imread(screenshot_path)
        print(f"Took new screenshot: {screenshot_path}")
    
    # Save the original screenshot for debugging
    if save_debug:
        cv2.imwrite("debug_screenshots/original.png", screenshot)
    
    # Get image dimensions
    height, width = screenshot.shape[:2]
    
    # Convert to HSV for better color detection
    hsv_image = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
    
    # Define green color range - ADJUSTED FOR MORE ACCURACY
    # GMX success message has a specific green shade
    lower_green = np.array([40, 100, 100])  # More saturated green
    upper_green = np.array([80, 255, 255])  # Upper bound for green
    
    # Create mask for green pixels
    mask = cv2.inRange(hsv_image, lower_green, upper_green)
    
    # Save debug image of the mask
    if save_debug:
        cv2.imwrite("debug_screenshots/green_mask.png", mask)
    
    # Clean up the mask to remove noise
    kernel = np.ones((5,5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    # Save cleaned mask
    if save_debug:
        cv2.imwrite("debug_screenshots/green_mask_cleaned.png", mask)
    
    # Find contours of green areas
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter and process contours that could be the success banner
    success_regions = []
    for contour in contours:
        # Get contour area
        area = cv2.contourArea(contour)
        
        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        
        # Calculate aspect ratio
        aspect_ratio = float(w) / h if h > 0 else 0
        
        # Calculate relative position in the image (as percentages)
        rel_y = y / height * 100  # Vertical position as percentage
        
        # Debugging information
        print(f"Green region found: Area={area}, Size={w}x{h}, Aspect ratio={aspect_ratio:.2f}, Y-Position={rel_y:.1f}%")
        
        # SUCCESS BANNER CRITERIA - ADJUSTED
        # The success banner is typically a wide green rectangle in the upper part of the page,
        # but not at the very top (which would be the GMX header).
        # It's also usually very wide compared to its height.
        if (area > 5000 and            # Sufficient size
            w > 300 and               # Wide enough for a message banner
            h > 20 and h < 80 and     # Not too tall or short for a banner
            aspect_ratio > 6.0 and    # Rectangular banner shape (wider)
            rel_y > 8 and rel_y < 25): # Upper part of page but not header
            
            # This looks like our success banner!
            success_regions.append((x, y, w, h, area))
            
    # If we found potential success regions
    if success_regions:
        # Sort by area to get the largest matching region
        success_regions.sort(key=lambda r: r[4], reverse=True)
        
        # Get the largest matching region
        x, y, w, h, area = success_regions[0]
        
        if save_debug:
            debug_img = screenshot.copy()
            cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 0, 255), 3)
            cv2.putText(debug_img, "SUCCESS BANNER", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
            cv2.imwrite("debug_screenshots/success_banner_detected.png", debug_img)
        
        # Save just this region for potential OCR
        success_region = screenshot[y:y+h, x:x+w]
        if save_debug and success_region.size > 0:
            cv2.imwrite("debug_screenshots/success_region.png", success_region)
        
        print(f"✅ Success banner detected at: ({x}, {y}) with size: {w}x{h}")
        return True
    
    print("❌ No success banner detected")
    return False

def detect_white_text_in_green_region(screenshot_path=None, save_debug=True):
    """
    Detects the white text within the green banner
    This method doesn't require OCR and can be more reliable
    
    Args:
        screenshot_path (str): Path to existing screenshot, or None to take a new one
        save_debug (bool): Whether to save debug images
        
    Returns:
        bool: True if white text in green banner detected, False otherwise
    """
    # Make debug directory
    os.makedirs("debug_screenshots", exist_ok=True)
    
    # If we have a success region from the green banner detection, analyze it
    region_path = "debug_screenshots/success_region.png"
    if os.path.exists(region_path):
        # Load the success region
        success_region = cv2.imread(region_path)
        
        # Convert to grayscale
        gray = cv2.cvtColor(success_region, cv2.COLOR_BGR2GRAY)
        
        # Apply threshold to find white text - ADJUSTED FOR BETTER TEXT DETECTION
        _, white_mask = cv2.threshold(gray, 220, 255, cv2.THRESH_BINARY)  # Higher threshold for white
        
        # Save the white mask
        if save_debug:
            cv2.imwrite("debug_screenshots/white_text_mask.png", white_mask)
        
        # Count white pixels
        white_pixel_count = cv2.countNonZero(white_mask)
        region_area = success_region.shape[0] * success_region.shape[1]
        white_ratio = white_pixel_count / region_area if region_area > 0 else 0
        
        print(f"White pixel ratio in success banner: {white_ratio:.3f}")
        
        # Use connected components to analyze the shapes in the white mask
        connectivity = 8
        output = cv2.connectedComponentsWithStats(white_mask, connectivity, cv2.CV_32S)
        (numLabels, labels, stats, centroids) = output
        
        # ADJUSTED TEXT DETECTION CRITERIA
        # If we have a reasonable number of connected components (not noise)
        if 2 <= numLabels <= 30:  # Success messages have multiple text components
            text_like_components = 0
            
            for i in range(1, numLabels):  # Skip background (0)
                x = stats[i, cv2.CC_STAT_LEFT]
                y = stats[i, cv2.CC_STAT_TOP]
                w = stats[i, cv2.CC_STAT_WIDTH]
                h = stats[i, cv2.CC_STAT_HEIGHT]
                area = stats[i, cv2.CC_STAT_AREA]
                
                # Text components tend to have these characteristics
                if (area > 20 and area < 1000 and  # Not too small or large
                    w > 3 and h > 3 and           # Minimum dimensions
                    w < 100 and h < 50):          # Maximum dimensions
                    text_like_components += 1
            
            # Success messages typically have several small components for checkmark and text
            if text_like_components >= 4:  # Adjusted threshold
                print(f"✅ Text components detected in success banner: {text_like_components}")
                return True
        
        # Backup method: reasonable amount of white pixels for text
        # Success message text typically has white ratio between 0.05 and 0.3
        if 0.05 < white_ratio < 0.3:  # Adjusted for more accuracy
            print(f"✅ White text detected in success banner (white ratio: {white_ratio:.3f})!")
            return True
    
    # If we don't have a success region or text wasn't detected
    return False

def detect_success_combined(screenshot_path=None, methods=None):
    """
    Uses multiple methods to detect success message
    
    Args:
        screenshot_path (str): Path to existing screenshot, or None to take a new one
        methods (list): List of detection methods to use, or None for all
        
    Returns:
        bool: True if success detected by any method
    """
    # Create debug directory if it doesn't exist
    os.makedirs("debug_screenshots", exist_ok=True)
    
    # Take new screenshot if needed
    if not screenshot_path or not os.path.exists(screenshot_path):
        screenshot_path = "debug_screenshots/current_screen.png"
        pyautogui.screenshot().save(screenshot_path)
        print(f"Took new screenshot: {screenshot_path}")
    
    print(f"\n🔍 CHECKING FOR SUCCESS MESSAGE USING MULTIPLE METHODS")
    print(f"Screenshot: {screenshot_path}")
    print("-" * 60)
    
    # Method 1: Green region detection
    print("\nMethod 1: Detect green success banner")
    success1 = detect_success_by_green_region(screenshot_path)
    
    # Method 2: White text in green region
    print("\nMethod 2: Detect white text in green banner")
    success2 = detect_white_text_in_green_region(screenshot_path)
    
    # Optional Method 3: OCR detection (if pytesseract is installed)
    success3 = False
    try:
        if 'pytesseract' in globals():
            print("\nMethod 3: OCR Text detection")
            # Add OCR code here if needed
    except:
        pass
    
    # Combined result
    success = success1 or success2 or success3
    
    # Final result
    print("\n" + "=" * 60)
    if success:
        print("✅ FINAL RESULT: Settings were successfully saved!")
    else:
        print("❌ FINAL RESULT: Success message not detected")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    print("🔍 Success Message Detection Test")
    print("=" * 60)
    
    # Path to test screenshot
    test_screenshot = "temp_settings_check.png"
    
    if not os.path.exists(test_screenshot):
        print(f"Warning: Test screenshot {test_screenshot} not found.")
        print("Taking a new screenshot in 3 seconds...")
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        test_screenshot = None
    
    # Run detection
    success = detect_success_combined(test_screenshot)
