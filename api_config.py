#!/usr/bin/env python3
"""
API Configuration Management

This module provides centralized configuration for OpenRouter API keys
and settings. It supports loading from environment variables, config files,
and provides defaults.
"""

import os
import json
from typing import List, Dict, Any, Optional


class APIConfig:
    """Configuration manager for OpenRouter API settings"""
    
    # Default API keys (can be overridden)
    DEFAULT_API_KEYS = [
        "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",
        "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"
    ]
    
    DEFAULT_BASE_URL = "https://openrouter.ai/api/v1"
    DEFAULT_TIMEOUT = 30
    DEFAULT_MAX_RETRIES = None  # Will use number of API keys
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize API configuration.
        
        Args:
            config_file: Optional path to JSON config file
        """
        self.config_file = config_file
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from various sources"""
        config = {
            "api_keys": self.DEFAULT_API_KEYS.copy(),
            "base_url": self.DEFAULT_BASE_URL,
            "timeout": self.DEFAULT_TIMEOUT,
            "max_retries": self.DEFAULT_MAX_RETRIES
        }
        
        # 1. Load from config file if provided
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    file_config = json.load(f)
                    config.update(file_config)
                print(f"✅ Loaded configuration from {self.config_file}")
            except Exception as e:
                print(f"⚠️ Failed to load config file {self.config_file}: {e}")
        
        # 2. Override with environment variables
        env_keys = os.getenv('OPENROUTER_API_KEYS')
        if env_keys:
            # Support comma-separated keys in environment variable
            config["api_keys"] = [key.strip() for key in env_keys.split(',') if key.strip()]
            print(f"✅ Loaded {len(config['api_keys'])} API keys from environment")
        
        env_base_url = os.getenv('OPENROUTER_BASE_URL')
        if env_base_url:
            config["base_url"] = env_base_url
            print(f"✅ Using base URL from environment: {env_base_url}")
        
        env_timeout = os.getenv('OPENROUTER_TIMEOUT')
        if env_timeout:
            try:
                config["timeout"] = int(env_timeout)
                print(f"✅ Using timeout from environment: {env_timeout}s")
            except ValueError:
                print(f"⚠️ Invalid timeout in environment: {env_timeout}")
        
        return config
    
    def get_api_keys(self) -> List[str]:
        """Get the list of API keys"""
        return self._config["api_keys"].copy()
    
    def get_base_url(self) -> str:
        """Get the base URL for API requests"""
        return self._config["base_url"]
    
    def get_timeout(self) -> int:
        """Get the request timeout in seconds"""
        return self._config["timeout"]
    
    def get_max_retries(self) -> Optional[int]:
        """Get the maximum number of retries"""
        return self._config["max_retries"]
    
    def add_api_key(self, api_key: str) -> None:
        """Add a new API key to the configuration"""
        if api_key not in self._config["api_keys"]:
            self._config["api_keys"].append(api_key)
            print(f"✅ Added new API key (total: {len(self._config['api_keys'])})")
        else:
            print("⚠️ API key already exists in configuration")
    
    def remove_api_key(self, api_key: str) -> bool:
        """Remove an API key from the configuration"""
        if api_key in self._config["api_keys"]:
            self._config["api_keys"].remove(api_key)
            print(f"✅ Removed API key (remaining: {len(self._config['api_keys'])})")
            return True
        else:
            print("⚠️ API key not found in configuration")
            return False
    
    def save_config(self, config_file: Optional[str] = None) -> bool:
        """
        Save current configuration to a JSON file.
        
        Args:
            config_file: Optional path to save to (defaults to initialized config_file)
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        target_file = config_file or self.config_file
        if not target_file:
            print("❌ No config file specified for saving")
            return False
        
        try:
            # Create a clean config for saving (remove sensitive keys if needed)
            save_config = self._config.copy()
            
            with open(target_file, 'w') as f:
                json.dump(save_config, f, indent=2)
            
            print(f"✅ Configuration saved to {target_file}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to save configuration: {e}")
            return False
    
    def validate_config(self) -> bool:
        """Validate the current configuration"""
        issues = []
        
        # Check API keys
        if not self._config["api_keys"]:
            issues.append("No API keys configured")
        else:
            for i, key in enumerate(self._config["api_keys"]):
                if not key or not key.startswith("sk-or-v1-"):
                    issues.append(f"API key {i+1} appears invalid: {key[:20]}...")
        
        # Check base URL
        if not self._config["base_url"]:
            issues.append("No base URL configured")
        elif not self._config["base_url"].startswith("https://"):
            issues.append("Base URL should use HTTPS")
        
        # Check timeout
        if self._config["timeout"] <= 0:
            issues.append("Timeout must be positive")
        
        if issues:
            print("❌ Configuration validation failed:")
            for issue in issues:
                print(f"   • {issue}")
            return False
        else:
            print("✅ Configuration validation passed")
            return True
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration"""
        return {
            "api_keys_count": len(self._config["api_keys"]),
            "api_keys_preview": [key[:20] + "..." for key in self._config["api_keys"]],
            "base_url": self._config["base_url"],
            "timeout": self._config["timeout"],
            "max_retries": self._config["max_retries"],
            "config_file": self.config_file
        }


# Global configuration instance
_global_config = None

def get_api_config(config_file: Optional[str] = None) -> APIConfig:
    """Get the global API configuration instance"""
    global _global_config
    if _global_config is None or config_file:
        _global_config = APIConfig(config_file)
    return _global_config


def load_config_from_file(config_file: str) -> APIConfig:
    """Load configuration from a specific file"""
    return APIConfig(config_file)


# Example configuration file format
EXAMPLE_CONFIG = {
    "api_keys": [
        "sk-or-v1-your-first-api-key-here",
        "sk-or-v1-your-second-api-key-here"
    ],
    "base_url": "https://openrouter.ai/api/v1",
    "timeout": 30,
    "max_retries": None
}


if __name__ == "__main__":
    # Demo the configuration system
    print("API Configuration Demo")
    print("=" * 40)
    
    # Create config instance
    config = get_api_config()
    
    # Show current configuration
    print("Current Configuration:")
    summary = config.get_summary()
    for key, value in summary.items():
        print(f"   {key}: {value}")
    
    print()
    
    # Validate configuration
    print("🔍 Validating Configuration:")
    config.validate_config()
    
    print()
    
    # Show example config file format
    print("📄 Example config.json format:")
    print(json.dumps(EXAMPLE_CONFIG, indent=2))
