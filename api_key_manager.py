#!/usr/bin/env python3
"""
OpenRouter API Key Manager with Rotation and Fallback

This module provides centralized API key management for OpenRouter requests
with automatic rotation and fallback functionality.

Features:
- Multiple API key support with automatic rotation
- Fallback to other keys when one fails
- Thread-safe operations for concurrent usage
- Comprehensive error handling and logging
- Request retry logic with exponential backoff
"""

import requests
import time
import threading
from typing import List, Optional, Dict, Any, Callable
from random import uniform
from api_config import get_api_config


class OpenRouterAPIManager:
    """
    Centralized API key manager for OpenRouter requests with rotation and fallback.
    """
    
    def __init__(self, api_keys: List[str], base_url: str = "https://openrouter.ai/api/v1"):
        """
        Initialize the API manager with multiple keys.
        
        Args:
            api_keys: List of OpenRouter API keys
            base_url: Base URL for OpenRouter API
        """
        if not api_keys:
            raise ValueError("At least one API key must be provided")
        
        self.api_keys = api_keys.copy()
        self.base_url = base_url
        self.current_key_index = 0
        self._lock = threading.Lock()  # Thread safety for concurrent usage
        
        # Track key performance for intelligent rotation
        self.key_stats = {i: {"success": 0, "failures": 0, "last_used": 0} for i in range(len(api_keys))}
        
        print(f"OpenRouter API Manager initialized with {len(self.api_keys)} keys")
    
    def get_current_api_key(self) -> str:
        """Get the current API key (thread-safe)"""
        with self._lock:
            return self.api_keys[self.current_key_index]
    
    def rotate_api_key(self) -> None:
        """Rotate to the next API key (thread-safe)"""
        with self._lock:
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            print(f"Rotated to API key #{self.current_key_index + 1}")
    
    def get_best_key_index(self) -> int:
        """Get the index of the best performing key based on success rate"""
        with self._lock:
            best_index = 0
            best_score = -1
            
            for i, stats in self.key_stats.items():
                total_requests = stats["success"] + stats["failures"]
                if total_requests == 0:
                    score = 1.0  # New key gets priority
                else:
                    success_rate = stats["success"] / total_requests
                    # Factor in recency (prefer recently successful keys)
                    recency_factor = max(0.1, 1.0 - (time.time() - stats["last_used"]) / 3600)
                    score = success_rate * recency_factor
                
                if score > best_score:
                    best_score = score
                    best_index = i
            
            return best_index
    
    def update_key_stats(self, key_index: int, success: bool) -> None:
        """Update statistics for a specific key"""
        with self._lock:
            if success:
                self.key_stats[key_index]["success"] += 1
            else:
                self.key_stats[key_index]["failures"] += 1
            self.key_stats[key_index]["last_used"] = time.time()
    
    def make_request(self, 
                    endpoint: str, 
                    method: str = "POST", 
                    payload: Optional[Dict[str, Any]] = None,
                    additional_headers: Optional[Dict[str, str]] = None,
                    max_retries: Optional[int] = None,
                    timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        Make API request with automatic key rotation and fallback.
        
        Args:
            endpoint: API endpoint (e.g., "/chat/completions", "/credits")
            method: HTTP method (GET, POST, etc.)
            payload: Request payload for POST requests
            additional_headers: Additional headers to include
            max_retries: Maximum number of retries (defaults to number of API keys)
            timeout: Request timeout in seconds
            
        Returns:
            API response dict or None if all keys failed
        """
        if max_retries is None:
            max_retries = len(self.api_keys)
        
        original_key_index = self.current_key_index
        
        for attempt in range(max_retries):
            # Use the best performing key for the first attempt
            if attempt == 0:
                key_index = self.get_best_key_index()
                with self._lock:
                    self.current_key_index = key_index
            
            current_key = self.get_current_api_key()
            
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {current_key}",
                "Content-Type": "application/json"
            }
            if additional_headers:
                headers.update(additional_headers)
            
            try:
                print(f"Using API key #{self.current_key_index + 1} (attempt {attempt + 1}/{max_retries})")
                
                # Make the request
                url = f"{self.base_url}{endpoint}"
                if method.upper() == "GET":
                    response = requests.get(url, headers=headers, timeout=timeout)
                elif method.upper() == "POST":
                    response = requests.post(url, headers=headers, json=payload, timeout=timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                print(f"📡 Response status: {response.status_code}")
                
                # Handle successful response
                if response.status_code == 200:
                    if not response.text.strip():
                        print("Empty response from API")
                        self.update_key_stats(self.current_key_index, False)
                        self.rotate_api_key()
                        continue
                    
                    # Success - update stats and rotate for load distribution
                    self.update_key_stats(self.current_key_index, True)
                    self.rotate_api_key()
                    return response.json()
                
                # Handle specific error codes
                elif response.status_code == 401:
                    print(f"API key #{self.current_key_index + 1} authentication failed")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    continue
                
                elif response.status_code == 429:
                    print(f"API key #{self.current_key_index + 1} rate limited")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    # Add delay for rate limiting
                    time.sleep(uniform(1.0, 3.0))
                    continue
                
                elif response.status_code >= 500:
                    print(f"Server error {response.status_code}, trying next key")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    continue
                
                else:
                    print(f"API Error {response.status_code}: {response.text}")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    continue
                    
            except requests.exceptions.Timeout:
                print(f"Request timeout with API key #{self.current_key_index + 1}")
                self.update_key_stats(self.current_key_index, False)
                self.rotate_api_key()
                continue
                
            except requests.exceptions.RequestException as e:
                print(f"Network error with API key #{self.current_key_index + 1}: {e}")
                self.update_key_stats(self.current_key_index, False)
                self.rotate_api_key()
                continue
                
            except Exception as e:
                print(f"Unexpected error with API key #{self.current_key_index + 1}: {e}")
                self.update_key_stats(self.current_key_index, False)
                self.rotate_api_key()
                continue
        
        # All keys failed, reset to original key
        with self._lock:
            self.current_key_index = original_key_index
        print(f"All {max_retries} API key attempts failed")
        return None
    
    def check_credits(self, min_threshold: float = 1.0) -> tuple:
        """
        Check OpenRouter API credits with fallback support.
        
        Args:
            min_threshold: Minimum credit threshold required
            
        Returns:
            tuple: (success: bool, credits: float, message: str)
        """
        response = self.make_request("/credits", method="GET")
        
        if not response:
            return False, 0.0, "Failed to check credits - all API keys failed"
        
        try:
            # Extract credit information from response
            if 'data' in response:
                credit_info = response['data']
                
                if isinstance(credit_info, dict):
                    total_credits = float(credit_info.get('total_credits', 0.0))
                    total_usage = float(credit_info.get('total_usage', 0.0))
                    available_credits = total_credits - total_usage
                    
                    print(f"OpenRouter Credits Available: ${available_credits:.4f}")
                    
                    if available_credits >= min_threshold:
                        return True, available_credits, "Sufficient credits available"
                    else:
                        error_msg = f"Insufficient credits: ${available_credits:.4f} < ${min_threshold:.2f} required"
                        return False, available_credits, error_msg
                else:
                    return False, 0.0, "Unexpected credit info format"
            else:
                return False, 0.0, "Could not determine credit balance from API response"
                
        except Exception as e:
            return False, 0.0, f"Error parsing credit response: {str(e)}"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics for all API keys"""
        with self._lock:
            stats = {}
            for i, key_stats in self.key_stats.items():
                total = key_stats["success"] + key_stats["failures"]
                success_rate = key_stats["success"] / total if total > 0 else 0.0
                stats[f"key_{i+1}"] = {
                    "success_rate": f"{success_rate:.2%}",
                    "total_requests": total,
                    "last_used": key_stats["last_used"]
                }
            return stats


# Global instance for easy access
_global_api_manager = None

def get_api_manager() -> OpenRouterAPIManager:
    """Get the global API manager instance"""
    global _global_api_manager
    if _global_api_manager is None:
        # Load configuration and create API manager
        config = get_api_config()
        api_keys = config.get_api_keys()
        base_url = config.get_base_url()
        _global_api_manager = OpenRouterAPIManager(api_keys, base_url)
    return _global_api_manager

def initialize_api_manager(api_keys: List[str]) -> OpenRouterAPIManager:
    """Initialize the global API manager with custom keys"""
    global _global_api_manager
    _global_api_manager = OpenRouterAPIManager(api_keys)
    return _global_api_manager
