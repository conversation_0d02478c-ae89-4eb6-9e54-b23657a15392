"""
Captcha Solver Example
This script demonstrates how to use the captcha solving functionality.
"""

from auto_clicker_bb import ImageClicker
import pyautogui
import time

def solve_captcha_example():
    """Example of how to solve a captcha when it appears."""
    
    # Your SolveCaptcha API key - replace with your actual key
    API_KEY = "YOUR_SOLVECAPTCHA_API_KEY_HERE"
    
    # Create clicker instance
    clicker = ImageClicker(confidence=0.8, wait_time=2)
    
    print("Captcha Solver Example")
    print("=" * 40)
    
    # Method 1: Wait for captcha dialog and solve it
    print("\n1. Waiting for captcha dialog...")
    time.sleep(5)
    location = clicker.wait_for_image("screens/captcha_dialog.png", timeout=25)
    
    if location:
        print("✅ Captcha dialog detected!")
        
        # Capture the captcha area (adjust coordinates as needed)
        # These coordinates are relative to the captcha dialog location
        captcha_x = 1096
        captcha_y = 743
        captcha_width = 300          
        captcha_height = 80
        
        print(f"Capturing captcha area at: ({captcha_x}, {captcha_y})")
        captcha_image = clicker.capture_captcha_area(
            captcha_x, captcha_y, 
            captcha_width, captcha_height, 
            "current_captcha.png"
        )
        
        # Solve the captcha using multipart method
        print("Sending captcha to SolveCaptcha service...")
        solution = None
        if solution:
            print(f"🎉 Captcha solved successfully: {solution}")
            
            # Type the solution in the captcha input field
            input_location = clicker.wait_for_image("screens/captcha_input.png", timeout=5)
            if input_location:
                print("Typing solution into input field...")
                pyautogui.click(input_location)
                time.sleep(0.5)
                pyautogui.typewrite(solution)
                print(f"✅ Typed solution: {solution}")
                
                # Click submit button if needed
                submit_location = clicker.wait_for_image("screens/captcha_submit.png", timeout=3)
                if submit_location:
                    pyautogui.click(submit_location)
                    print("✅ Clicked submit button")
            else:
                print("⚠️ Captcha input field not found")
                print(f"Manual entry required: {solution}")
        else:
            print("❌ Failed to solve captcha")
    else:
        print("ℹ️ No captcha dialog detected")

def solve_captcha_base64_example():
    """Example using base64 method instead of multipart."""
    
    API_KEY = "YOUR_SOLVECAPTCHA_API_KEY_HERE"
    clicker = ImageClicker()
    
    print("\n2. Base64 Captcha Solving Example")
    print("-" * 40)
    
    # Take a screenshot of captcha area first
    captcha_image = "test_captcha.png"
    
    # You can manually save a captcha image for testing
    # Or capture it programmatically
    
    if clicker.solve_captcha_base64(captcha_image, API_KEY):
        print("Captcha solved using base64 method!")

def automated_workflow_with_captcha():
    """Complete workflow that handles captcha automatically."""
    
    API_KEY = "YOUR_SOLVECAPTCHA_API_KEY_HERE"
    clicker = ImageClicker(confidence=0.8)
    
    print("\n3. Automated Workflow with Captcha Handling")
    print("-" * 50)
    
    try:
        # Step 1: Click settings
        if clicker.wait_and_click_image("screens/settings_icon.png", wait_time=2):
            print("✅ Clicked settings")
        
        # Step 2: Enable POP3
        if clicker.wait_and_click_image("screens/pop3.png", wait_time=1):
            print("✅ Clicked POP3 option")
        
        if clicker.wait_and_click_image("screens/enable_pop3.png", wait_time=1):
            print("✅ Enabled POP3")
        
        # Step 3: Save settings
        if clicker.wait_and_click_image("screens/save.png", wait_time=1):
            print("✅ Clicked save")
        
        # Step 4: Handle captcha if it appears
        print("Checking for captcha...")
        location = clicker.wait_for_image("screens/captcha_dialog.png", timeout=5)
        
        if location:
            print("🔒 Captcha detected! Solving...")
            
            # Capture captcha
            captcha_x = 1096
            captcha_y = 743
            captcha_width = 295
            captcha_height = 74
            captcha_image = clicker.capture_captcha_area(
                captcha_x, captcha_y, 200, 80, "workflow_captcha.png"
            )
            
            print(captcha_image)
        
    except Exception as e:
        print(f"❌ Error in workflow: {e}")
        return False

if __name__ == "__main__":
    print("SolveCaptcha Integration Examples")
    print("=" * 50)
    
    print("\n⚠️ IMPORTANT: Replace 'YOUR_SOLVECAPTCHA_API_KEY_HERE' with your actual API key!")
    print("Get your API key from: https://solvecaptcha.com/")
    
    print("\n📝 Available examples:")
    print("1. solve_captcha_example() - Basic captcha solving")
    print("2. solve_captcha_base64_example() - Using base64 method")
    print("3. automated_workflow_with_captcha() - Complete workflow")
    
    # Uncomment the example you want to run:
    
    solve_captcha_example()
    # solve_captcha_base64_example()
    # automated_workflow_with_captcha()
    
    print("\nUncomment the function you want to test and run the script!")
