"""
Success Message Detection using Multiple Methods

This script provides multiple methods for detecting the 'Settings were saved successfully' message:
1. OCR - Optical Character Recognition to detect the text
2. Green Check Detection - Finds the green checkmark icon
3. Color Region Detection - Detects the green banner
"""

import pyautogui
import cv2
import numpy as np
import os
import time
from PIL import Image

# Uncomment to use Tesseract OCR if installed
# import pytesseract
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def detect_success_by_green_region(screenshot_path=None, save_debug=True):
    """
    Detects the green success banner by its distinctive color and shape
    
    Args:
        screenshot_path (str): Path to existing screenshot, or None to take a new one
        save_debug (bool): Whether to save debug images
        
    Returns:
        bool: True if green success banner detected, False otherwise
    """
    # Make debug directory
    os.makedirs("debug_screenshots", exist_ok=True)
    
    # Take screenshot or load existing
    if screenshot_path and os.path.exists(screenshot_path):
        screenshot = cv2.imread(screenshot_path)
        print(f"Using existing screenshot: {screenshot_path}")
    else:
        screenshot_path = "debug_screenshots/current_screen.png"
        screenshot = pyautogui.screenshot()
        screenshot.save(screenshot_path)
        screenshot = cv2.imread(screenshot_path)
        print(f"Took new screenshot: {screenshot_path}")
    
    # Save the original screenshot for debugging
    if save_debug:
        cv2.imwrite("debug_screenshots/original.png", screenshot)
    
    # Get image dimensions
    height, width = screenshot.shape[:2]
    
    # Convert to HSV for better color detection
    hsv_image = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
    
    # Define green color range (adjust as needed)
    lower_green = np.array([40, 40, 40])  # Lower bound for green
    upper_green = np.array([80, 255, 255])  # Upper bound for green
    
    # Create mask for green pixels
    mask = cv2.inRange(hsv_image, lower_green, upper_green)
    
    # Save debug image of the mask
    if save_debug:
        cv2.imwrite("debug_screenshots/green_mask.png", mask)
    
    # Clean up the mask to remove noise
    kernel = np.ones((5,5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    # Save cleaned mask
    if save_debug:
        cv2.imwrite("debug_screenshots/green_mask_cleaned.png", mask)
    
    # Find contours of green areas
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter and process contours that could be the success banner
    for contour in contours:
        # Get contour area
        area = cv2.contourArea(contour)
        
        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        
        # Calculate aspect ratio
        aspect_ratio = float(w) / h if h > 0 else 0
        
        # Calculate relative position in the image (as percentages)
        rel_y = y / height * 100  # Vertical position as percentage
        
        # Debugging information
        print(f"Green region found: Area={area}, Size={w}x{h}, Aspect ratio={aspect_ratio:.2f}, Y-Position={rel_y:.1f}%")
        
        # Filter based on shape characteristics AND position
        # Success message is typically:
        # 1. Wide rectangle (high aspect ratio)
        # 2. Not at the very top (excludes GMX header)
        # 3. Positioned in the upper-middle portion of the page (typically 10-30% down)
        # 4. Has significant width
        if (area > 5000 and            # Sufficient size
            w > 200 and               # Wide enough
            aspect_ratio > 3.0 and    # Rectangular banner shape
            rel_y > 5 and rel_y < 30): # Not at the very top, but in the upper portion
                                       # 0-5% would be the GMX header
            
            # This looks like our success banner!
            if save_debug:
                debug_img = screenshot.copy()
                cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                cv2.putText(debug_img, "SUCCESS BANNER", (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
                cv2.imwrite("debug_screenshots/success_banner_detected.png", debug_img)
            
            # Save just this region for potential OCR
            success_region = screenshot[y:y+h, x:x+w]
            if save_debug and success_region.size > 0:
                cv2.imwrite("debug_screenshots/success_region.png", success_region)
            
            print(f"✅ Success banner detected at: ({x}, {y}) with size: {w}x{h}")
            return True
    
    print("❌ No success banner detected")
    return False

def detect_white_text_in_green_region(screenshot_path=None, save_debug=True):
    """
    Detects the white text within the green banner
    This method doesn't require OCR and can be more reliable
    
    Args:
        screenshot_path (str): Path to existing screenshot, or None to take a new one
        save_debug (bool): Whether to save debug images
        
    Returns:
        bool: True if white text in green banner detected, False otherwise
    """
    # Make debug directory
    os.makedirs("debug_screenshots", exist_ok=True)
    
    # Take screenshot or load existing if no green banner detection already done
    if not os.path.exists("debug_screenshots/success_region.png"):
        # Take screenshot or load existing
        if screenshot_path and os.path.exists(screenshot_path):
            screenshot = cv2.imread(screenshot_path)
        else:
            screenshot_path = "debug_screenshots/current_screen.png"
            screenshot = pyautogui.screenshot()
            screenshot.save(screenshot_path)
            screenshot = cv2.imread(screenshot_path)
        
        # Get dimensions
        height, width = screenshot.shape[:2]
        
        # Focus on the area where success messages typically appear (upper portion, excluding header)
        # Typical banner appears around 10-20% from the top
        top_margin = int(height * 0.05)  # Skip the very top (header)
        banner_height = int(height * 0.2)  # Look in the top 20% of the screen
        
        # Crop to region of interest
        roi = screenshot[top_margin:top_margin+banner_height, 0:width]
        
        if save_debug:
            cv2.imwrite("debug_screenshots/notification_region.png", roi)
        
        # Look for success text directly
        # Convert to HSV to separate text from background
        hsv_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        
        # Look for bright white/light text (high value in HSV)
        lower_white = np.array([0, 0, 200])  # White text has low saturation, high value
        upper_white = np.array([180, 40, 255])
        white_mask = cv2.inRange(hsv_roi, lower_white, upper_white)
        
        if save_debug:
            cv2.imwrite("debug_screenshots/white_text_direct.png", white_mask)
        
        # Look for text-like structures (connected components of appropriate size)
        connectivity = 4  # 4 or 8 connectivity
        output = cv2.connectedComponentsWithStats(white_mask, connectivity, cv2.CV_32S)
        (numLabels, labels, stats, centroids) = output
        
        # Check for text-like components
        for i in range(1, numLabels):  # Skip background (0)
            x = stats[i, cv2.CC_STAT_LEFT]
            y = stats[i, cv2.CC_STAT_TOP]
            w = stats[i, cv2.CC_STAT_WIDTH]
            h = stats[i, cv2.CC_STAT_HEIGHT]
            area = stats[i, cv2.CC_STAT_AREA]
            
            # Text components have certain characteristics
            if area > 20 and area < 500 and w > 5 and h > 5 and w < 100:
                # This might be text
                if save_debug:
                    debug_roi = roi.copy()
                    cv2.rectangle(debug_roi, (x, y), (x+w, y+h), (0, 0, 255), 1)
                    cv2.imwrite("debug_screenshots/potential_text.png", debug_roi)
                
                # Check if there are multiple text components near each other (suggesting a message)
                text_count = 0
                for j in range(1, numLabels):
                    if j != i:
                        x2 = stats[j, cv2.CC_STAT_LEFT]
                        y2 = stats[j, cv2.CC_STAT_TOP]
                        if abs(y - y2) < 20:  # Text on the same line
                            text_count += 1
                
                if text_count > 3:  # Several text components = likely a message
                    print(f"✅ Text detected in notification area!")
                    return True
    
    # If we have a success region from the green banner detection, analyze it
    region_path = "debug_screenshots/success_region.png"
    if os.path.exists(region_path):
        # Load the success region
        success_region = cv2.imread(region_path)
        
        # Convert to grayscale
        gray = cv2.cvtColor(success_region, cv2.COLOR_BGR2GRAY)
        
        # Apply threshold to find white text
        _, white_mask = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
        
        # Save the white mask
        if save_debug:
            cv2.imwrite("debug_screenshots/white_text_mask.png", white_mask)
        
        # Count white pixels
        white_pixel_count = cv2.countNonZero(white_mask)
        region_area = success_region.shape[0] * success_region.shape[1]
        white_ratio = white_pixel_count / region_area if region_area > 0 else 0
        
        print(f"White pixel ratio in success banner: {white_ratio:.3f}")
        
        # Check for success text pattern - look for the checkmark icon and text
        # Success messages typically have:
        # 1. A small icon/checkmark on the left
        # 2. Text running horizontally
        
        # Use connected components to analyze the shapes in the white mask
        connectivity = 8
        output = cv2.connectedComponentsWithStats(white_mask, connectivity, cv2.CV_32S)
        (numLabels, labels, stats, centroids) = output
        
        # If we have a small number of components (not noise)
        if 2 <= numLabels <= 20:  # Adjust thresholds as needed
            text_found = False
            checkmark_found = False
            
            for i in range(1, numLabels):  # Skip background (0)
                x = stats[i, cv2.CC_STAT_LEFT]
                y = stats[i, cv2.CC_STAT_TOP]
                w = stats[i, cv2.CC_STAT_WIDTH]
                h = stats[i, cv2.CC_STAT_HEIGHT]
                area = stats[i, cv2.CC_STAT_AREA]
                
                # Check for checkmark-like component (small square-ish shape)
                if area > 20 and area < 500 and abs(w - h) < 10:
                    checkmark_found = True
                
                # Check for text-like component (wide, not tall)
                if w > h * 3 and area > 100:
                    text_found = True
            
            if checkmark_found and text_found:
                print(f"✅ Success message pattern detected in banner!")
                return True
        
        # Backup method: significant white pixels suggest text
        if 0.05 < white_ratio < 0.5:  # Adjusted threshold for more sensitivity
            print(f"✅ White text detected in success banner!")
            return True
    
    return False

def detect_success_combined(screenshot_path=None, methods=None):
    """
    Uses multiple methods to detect success message
    
    Args:
        screenshot_path (str): Path to existing screenshot, or None to take a new one
        methods (list): List of detection methods to use, or None for all
        
    Returns:
        bool: True if success detected by any method
    """
    # Create debug directory if it doesn't exist
    os.makedirs("debug_screenshots", exist_ok=True)
    
    # Take new screenshot if needed
    if not screenshot_path or not os.path.exists(screenshot_path):
        screenshot_path = "debug_screenshots/current_screen.png"
        pyautogui.screenshot().save(screenshot_path)
        print(f"Took new screenshot: {screenshot_path}")
    
    print(f"\n🔍 CHECKING FOR SUCCESS MESSAGE USING MULTIPLE METHODS")
    print(f"Screenshot: {screenshot_path}")
    print("-" * 60)
    
    # Method 1: Green region detection
    print("\nMethod 1: Detect green success banner")
    success1 = detect_success_by_green_region(screenshot_path)
    
    # Method 2: White text in green region
    print("\nMethod 2: Detect white text in green banner")
    success2 = detect_white_text_in_green_region(screenshot_path)
    
    # Optional Method 3: OCR detection (if pytesseract is installed)
    success3 = False
    try:
        if 'pytesseract' in globals():
            print("\nMethod 3: OCR Text detection")
            # Add OCR code here if needed
    except:
        pass
    
    # Combined result
    success = success1 or success2 or success3
    
    # Final result
    print("\n" + "=" * 60)
    if success:
        print("✅ FINAL RESULT: Settings were successfully saved!")
    else:
        print("❌ FINAL RESULT: Success message not detected")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    print("🔍 Success Message Detection Test")
    print("=" * 60)
    
    # Path to test screenshot
    test_screenshot = "temp_settings_check.png"
    
    if not os.path.exists(test_screenshot):
        print(f"Warning: Test screenshot {test_screenshot} not found.")
        print("Taking a new screenshot in 3 seconds...")
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        test_screenshot = None
    
    # Run detection
    success = detect_success_combined(test_screenshot)
