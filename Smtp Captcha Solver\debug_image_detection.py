#!/usr/bin/env python3
"""
Debug script for troubleshooting image detection issues.
Run this when you have a captcha or dialog open on screen.
"""

import pyautogui
import time
import os
from auto_clicker_bb import ImageClicker

def main():
    print("🔍 Image Detection Debug Tool")
    print("=" * 50)
    
    # Initialize clicker
    clicker = ImageClicker(confidence=0.8)
    
    print("This tool will help debug image detection issues.")
    print("Make sure your captcha dialog or target element is visible on screen!")
    
    # Give user time to prepare
    for i in range(5, 0, -1):
        print(f"Starting analysis in {i} seconds... (Position your screen now)")
        time.sleep(1)
    
    print("\n🏁 Starting analysis...")
    
    # 1. Take immediate screenshot
    print("\n1️⃣ Taking full screen screenshot...")
    screenshot_path = clicker.capture_full_screen_debug("current_screen.png")
    
    # 2. Test captcha dialog detection
    captcha_image = "screens/captcha_dialog.png"
    print(f"\n2️⃣ Testing captcha dialog detection...")
    if os.path.exists(captcha_image):
        clicker.debug_captcha_detection(captcha_image)
    else:
        print(f"❌ Captcha image not found: {captcha_image}")
        print("📝 You need to create this image by taking a screenshot of the captcha dialog")
    
    # 3. Test all images in screens folder
    print(f"\n3️⃣ Testing all available screen images...")
    screens_dir = "screens"
    if os.path.exists(screens_dir):
        for filename in os.listdir(screens_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_path = os.path.join(screens_dir, filename)
                print(f"\n   Testing: {filename}")
                
                # Quick detection test
                try:
                    result = pyautogui.locateOnScreen(image_path, confidence=0.7)
                    if result:
                        center = pyautogui.center(result)
                        print(f"   ✅ FOUND at {center}")
                    else:
                        print(f"   ❌ Not found")
                except Exception as e:
                    print(f"   ⚠️  Error: {e}")
    
    # 4. Interactive mode
    print(f"\n4️⃣ Interactive mode - Manual testing")
    while True:
        print(f"\nOptions:")
        print(f"1. Take another screenshot")
        print(f"2. Test specific image file")
        print(f"3. Show current mouse position")
        print(f"4. Exit")
        
        choice = input("Enter choice (1-4): ").strip()
        
        if choice == "1":
            screenshot_path = clicker.capture_full_screen_debug()
            print(f"Screenshot saved: {screenshot_path}")
            
        elif choice == "2":
            image_path = input("Enter image path: ").strip()
            if os.path.exists(image_path):
                analysis = clicker.analyze_screen_for_image(image_path)
                print(f"Analysis complete! Found {analysis['matches_found']} matches")
            else:
                print(f"Image not found: {image_path}")
                
        elif choice == "3":
            pos = pyautogui.position()
            print(f"Current mouse position: {pos}")
            
        elif choice == "4":
            break
        else:
            print("Invalid choice!")
    
    print(f"\n✅ Debug session complete!")
    print(f"📁 Check the 'debug_screenshots' folder for captured images")
    print(f"🔧 Use the analysis results to improve your image detection")

if __name__ == "__main__":
    main()
